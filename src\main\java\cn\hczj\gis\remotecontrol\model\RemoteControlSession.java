package cn.hczj.gis.remotecontrol.model;

import lombok.Data;

import javax.websocket.Session;
import java.time.LocalDateTime;

/**
 * 远程控制会话信息
 */
@Data
public class RemoteControlSession {
    
    /**
     * 控制码
     */
    private String controlCode;
    
    /**
     * 被控制端会话
     */
    private Session assistedSession;
    
    /**
     * 控制端会话
     */
    private Session controllerSession;
    
    /**
     * 被控制端用户ID
     */
    private String assistedUserId;
    
    /**
     * 控制端用户ID
     */
    private String controllerUserId;
    
    /**
     * 会话创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 会话状态
     */
    private SessionStatus status;
    
    /**
     * 控制码过期时间
     */
    private LocalDateTime expireTime;
    
    public enum SessionStatus {
        WAITING,    // 等待连接
        CONNECTED,  // 已连接
        DISCONNECTED // 已断开
    }
    
    public RemoteControlSession() {
        this.createTime = LocalDateTime.now();
        this.status = SessionStatus.WAITING;
    }
    
    /**
     * 检查控制码是否过期
     */
    public boolean isExpired() {
        return expireTime != null && LocalDateTime.now().isAfter(expireTime);
    }
    
    /**
     * 检查会话是否已建立连接
     */
    public boolean isConnected() {
        return status == SessionStatus.CONNECTED && 
               assistedSession != null && assistedSession.isOpen() &&
               controllerSession != null && controllerSession.isOpen();
    }
}
