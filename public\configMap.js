const IP_PORT_NAME = "//localhost:8090/gis-services/"
const GIS_SERVICES = "http:" + IP_PORT_NAME
const GIS_SYNERGY_MESSAGE_SERVICES = "ws:" + IP_PORT_NAME + "synergy-message/"
const LINKAGE_MESSAGE_SERVICES = "ws:" + IP_PORT_NAME + "linkage-message"
const IM_SERVICES = "ws:" + IP_PORT_NAME + "im-message"
const REMOTE_CONTROL_SERVICES = "ws://localhost:8090/remote-control"

const OVERVIEW_MAP_URL = "http://**************:9090/map-data-services/electronic/{z}/{x}/{-y}.png"

// 初始视点
const INIT_VIEWPORT = [115.96797, 39.48364, 54000.0]
// 初始方向
const INIT_ROTATION = [0.0, -90.0, 0.0]
// 初始飞行时间
const INIT_DURATION = 3

// 五分钟防控范围
const PREVENTION_AREA_5 = 5000
// 十分钟防控范围
const PREVENTION_AREA_10 = 10000
// 三十分钟防控范围
const PREVENTION_AREA_30 = 30000

// 全景图点位样式配置
const PANORAMA_STYLE = {
    IMAGE_URL: '',  // 全景图点位Icon，如："./visual-platform-new/static/img/panorama_blue.png"
    IMAGE_SIZE: [40, 40],  // 全景图点位Icon大小
    LABEL_SIZE: "16px 微软雅黑粗体",  // 全景图点位文字大小及字体
    LABEL_COLOR: "#ffffff",  // 全景图点位文字颜色
    LABEL_BACKGROUND_SHOW: true,  // 全景图点位文字背景色
    LABEL_BACKGROUND_COLOR: "rgba(0, 123, 255, 0.46)",  // 全景图点位文字背景色
    LABEL_BACKGROUND_PADDING: [6, 4],  // 全景图点位文字背景留白
    LABEL_PIXEL_OFFSET: [0, -34],  // 全景图点位文字像素偏移
    LABEL_STYLE: 2,  // 全景图点位文字样式，0：文字不描边，1：只描边，2：文字加描边
    LABEL_OUTLINE_COLOR: "#ffffff",  // 全景图点位文字描边颜色
    LABEL_OUTLINE_WIDTH: 1,  // 全景图点位文字描边宽度
}

// 联动目标点位样式配置
const LINKAGE_TARGET_STYLE = {
    IMAGE_SIZE: [35, 35],  // 联动目标点位Icon大小
    LABEL_SIZE: "18px 微软雅黑粗体",  // 联动目标点位文字大小及字体
    LABEL_COLOR: "#000000",  // 联动目标点位文字颜色
    LABEL_BACKGROUND_SHOW: true,  // 联动目标点位文字背景色
    LABEL_BACKGROUND_COLOR: "rgba(13, 245, 241, 0.47)",  // 联动目标点位文字背景色
    LABEL_BACKGROUND_PADDING: [8, 6],  // 联动目标点位文字背景留白
    LABEL_PIXEL_OFFSET: [0, -58],  // 联动目标点位文字像素偏移
    LABEL_STYLE: 2,  // 联动目标点位文字样式，0：文字不描边，1：只描边，2：文字加描边
    LABEL_OUTLINE_COLOR: "#ffffff",  // 联动目标点位文字描边颜色
    LABEL_OUTLINE_WIDTH: 5,  // 联动目标点位文字描边宽度
}

// 是否开启免登录模式（开启后不需要登录，不显示用户相关操作，不开启IM功能）
const NO_LOGIN_MODE = false

const PLATFORM_TITLE = "二三维一体化数字沙盘"
const PLATFORM_TITLE_FONT_SIZE = "38px"

// 沙盘地图配置
const MAP_MASK = [{
    name: "河北沙盘",
    instruction: "HeBeiShaPan",
    image: "./static/img/mapMask/hebei.png",
    dataUrl: "./static/json/mapMask/hebei.json",
    destination: [116.18182, 39.20212, 1450000.0],
    rotation: [0.0, -90.0, 0.0],
    duration: 3.0
}, {
    name: "河南沙盘",
    instruction: "HeNanShaPan",
    image: "./static/img/mapMask/henan.png",
    dataUrl: "./static/json/mapMask/henan.json",
    destination: [113.6058, 33.82848, 1250000.0],
    rotation: [0.0, -90.0, 0.0],
    duration: 3.0
}, {
    name: "台湾沙盘",
    instruction: "TaiWanShaPan",
    image: "./static/img/mapMask/taiwan.png",
    dataUrl: "./static/json/mapMask/taiwan.json",
    destination: [120.85963, 23.55704, 835000.0],
    rotation: [0.0, -90.0, 0.0],
    duration: 3.0
}
]
