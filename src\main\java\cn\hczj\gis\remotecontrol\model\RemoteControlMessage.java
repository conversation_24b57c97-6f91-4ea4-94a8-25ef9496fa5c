package cn.hczj.gis.remotecontrol.model;

import lombok.Data;

/**
 * 远程控制消息
 */
@Data
public class RemoteControlMessage {
    
    /**
     * 消息类型
     */
    private MessageType type;
    
    /**
     * 控制码
     */
    private String controlCode;
    
    /**
     * 命令类型
     */
    private String commandType;
    
    /**
     * 命令数据
     */
    private Object commandData;
    
    /**
     * 消息内容
     */
    private String message;
    
    /**
     * 状态码
     */
    private Integer status;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    public enum MessageType {
        // 连接相关
        REQUEST_CONTROL_CODE,   // 请求控制码
        CONTROL_CODE_RESPONSE,  // 控制码响应
        JOIN_SESSION,           // 加入会话
        SESSION_STATUS,         // 会话状态
        
        // 命令相关
        COMMAND,                // 控制命令
        COMMAND_RESPONSE,       // 命令响应
        
        // 状态相关
        HEARTBEAT,              // 心跳
        ERROR,                  // 错误
        DISCONNECT              // 断开连接
    }
    
    public RemoteControlMessage() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public RemoteControlMessage(MessageType type) {
        this();
        this.type = type;
    }
    
    public RemoteControlMessage(MessageType type, String message) {
        this(type);
        this.message = message;
    }
    
    public static RemoteControlMessage success(MessageType type, String message) {
        RemoteControlMessage msg = new RemoteControlMessage(type, message);
        msg.setStatus(200);
        return msg;
    }
    
    public static RemoteControlMessage error(String message) {
        RemoteControlMessage msg = new RemoteControlMessage(MessageType.ERROR, message);
        msg.setStatus(400);
        return msg;
    }
    
    public static RemoteControlMessage command(String commandType, Object commandData) {
        RemoteControlMessage msg = new RemoteControlMessage(MessageType.COMMAND);
        msg.setCommandType(commandType);
        msg.setCommandData(commandData);
        return msg;
    }
}
