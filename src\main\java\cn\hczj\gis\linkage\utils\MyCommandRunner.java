package cn.hczj.gis.linkage.utils;

import cn.hczj.gis.linkage.websocket.UdpWebSocketServer;
import cn.hczj.gis.utils.DeviceUniqueId;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.xml.bind.DatatypeConverter;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Data
@Component
@Slf4j
public class MyCommandRunner implements CommandLineRunner {

    @Value("${UDP_PORT}")
    private String UDP_PORT;
    @Value("${UDP_IP}")
    private String UDP_IP;

    @Value("${UDP_CLIENT_PORT}")
    private String UDP_CLIENT_PORT;
    @Value("${UDP_CLIENT_IP}")
    private String UDP_CLIENT_IP;

    public DatagramSocket socket;

    @Value("${server.port}")
    private String port;

    @PostConstruct
    public void dddd() throws Exception {
        log.info("UDP_PORT:{}", UDP_PORT);
        log.info("UDP_IP:{}", UDP_IP);
        socket = new DatagramSocket(Integer.parseInt(UDP_PORT), InetAddress.getByName(UDP_IP));
    }

    @Override
    public void run(String... args) {

        try {
            String md5Value = DigestUtil.md5Hex(DeviceUniqueId.getDeviceUniqueId());
            log.info("本机机器码：{}", md5Value);
            String osName = System.getProperty("os.name");
            System.out.println(osName);
            if (osName.startsWith("Mac OS")) {
                Runtime.getRuntime().exec("open     http://localhost:" + port + "/" + md5Value);
            } else if (osName.startsWith("Windows")) {
                Runtime.getRuntime().exec("cmd   /c   start   http://localhost:" + port + "/" + md5Value);
            } else {
                Runtime.getRuntime().exec("open     http://localhost:" + port + "/" + md5Value);
            }
            String localUploadPath = System.getProperty("user.dir") + "/verification.txt";
            String data = FileUtil.readString(localUploadPath, StandardCharsets.UTF_8).trim();
            String endDay = data.substring(32, data.length());
            String jimi = DigestUtil.md5Hex(md5Value + endDay).trim();
            log.info("本机加密后数据：{}", (jimi + endDay).toUpperCase());
            log.info("读取到的数据：{}", data);
//            if (StringUtils.isEmpty(data)) {
//                System.exit(0);
//            }
//            log.info("" + (jimi + endDay).toUpperCase().equals(data));
//
//            if (DateUtil.parse(endDay, "yyyyMMdd").before(new Date())) {
//                System.exit(0);
//            }
//            if (!(jimi + endDay).toUpperCase().equals(data)) {
//                System.exit(0);
//            }


            //////////////////////////////////////////////////////////////////////
            ExecutorService executor = Executors.newFixedThreadPool(2);
            // 启动UDP监听
            executor.submit(() -> {
                try {
                    byte[] bufferr = new byte[1024];
                    log.info("UDP服务端已启动，监听IP:{}，端口：{}", UDP_IP, UDP_PORT);
                    while (true) {
                        DatagramPacket packet = new DatagramPacket(bufferr, bufferr.length);
                        socket.receive(packet);
                        String ip = packet.getAddress().getHostAddress();
                        int port = packet.getPort();

                        HashMap<String,Object> resultMap = new HashMap<>();
                        String dadada= HexUtil.encodeHexStr(packet.getData());
                        if(!dadada.contains("d207000000")){
                            String msg = new String(packet.getData(), 0, packet.getLength());
                            resultMap.put("code", 2001);
                            resultMap.put("msg", msg);
                            log.info("收到UDP点位交互指令：{}", msg);
                            UdpWebSocketServer.GroupSentMessage(JSON.toJSONString(resultMap));
                            continue;
                        }
                        String msg=dadada.substring(0,112);
                        log.info("收到UDP不限点位交互指令：{}", msg);
                        String[] data1 =splitByNumber(msg, 2);
                        StringBuilder dresult = new StringBuilder();
                        int n = 1;
                        int j = 1;
                        for (int i = 0; i < data1.length; i++) {
                            dresult.append(data1[i]);
                            if (j == 8) {
                                if (n == 1) {
                                    ByteBuffer buffer = ByteBuffer.wrap(
                                            DatatypeConverter.parseHexBinary(dresult.toString())
                                    ).order(ByteOrder.LITTLE_ENDIAN);
                                    long littleEndianValue = buffer.getLong();  // 0x00000000000007D2 = 2002
                                    j = 0;
                                    dresult.setLength(0);
                                    resultMap.put("code",littleEndianValue);
                                }
                                if (n == 2) {
                                    String hexStr = dresult.toString();
                                    StringBuilder reversed = new StringBuilder();
                                    for (int d = hexStr.length() - 2; d >= 0; d -= 2) {
                                        reversed.append(hexStr.substring(d,  d + 2));
                                    }
                                    long value = Long.parseLong(reversed.toString(), 16);
                                    double value1 = Double.longBitsToDouble(value);
                                    j = 0;
                                    dresult.setLength(0);
                                    resultMap.put("longitude",value1);
                                }
                                if (n == 3) {
                                    String hexStr = dresult.toString();
                                    StringBuilder reversed = new StringBuilder();
                                    for (int d = hexStr.length() - 2; d >= 0; d -= 2) {
                                        reversed.append(hexStr.substring(d,  d + 2));
                                    }
                                    long value = Long.parseLong(reversed.toString(), 16);
                                    double value1 = Double.longBitsToDouble(value);
                                    j = 0;
                                    dresult.setLength(0);
                                    resultMap.put("latitude",value1);
                                }
                                if (n == 4) {
//                                    String hexStr = dresult.toString();
//                                    StringBuilder reversed = new StringBuilder();
//                                    for (int d = hexStr.length() - 2; d >= 0; d -= 2) {
//                                        reversed.append(hexStr.substring(d,  d + 2));
//                                    }
//                                    long value = Long.parseLong(reversed.toString(), 16);
//                                    double value1 = Double.longBitsToDouble(value);
                                    j = 0;
                                    dresult.setLength(0);
                                    resultMap.put("altitude",20000);
                                }
                                if (n == 5) {
                                    String hexStr = dresult.toString();
                                    StringBuilder reversed = new StringBuilder();
                                    for (int d = hexStr.length() - 2; d >= 0; d -= 2) {
                                        reversed.append(hexStr.substring(d,  d + 2));
                                    }
                                    long value = Long.parseLong(reversed.toString(), 16);
                                    double value1 = Double.longBitsToDouble(value);
                                    j = 0;
                                    dresult.setLength(0);
                                    resultMap.put("range",value1);
                                }
                                if (n == 6) {
                                    String hexStr = dresult.toString();
                                    StringBuilder reversed = new StringBuilder();
                                    for (int d = hexStr.length() - 2; d >= 0; d -= 2) {
                                        reversed.append(hexStr.substring(d,  d + 2));
                                    }
                                    long value = Long.parseLong(reversed.toString(), 16);
                                    double value1 = Double.longBitsToDouble(value);
                                    j = 0;
                                    dresult.setLength(0);
                                    resultMap.put("heading",value1);
                                }
                                if (n == 7) {
                                    String hexStr = dresult.toString();
                                    StringBuilder reversed = new StringBuilder();
                                    for (int d = hexStr.length() - 2; d >= 0; d -= 2) {
                                        reversed.append(hexStr.substring(d,  d + 2));
                                    }
                                    long value = Long.parseLong(reversed.toString(), 16);
                                    double value1 = Double.longBitsToDouble(value);
                                    j = 0;
                                    dresult.setLength(0);
                                    resultMap.put("pitch",value1);
                                }
                                n++;
                            }
                            j++;
                        }
                        log.info("不限点位交互信息：{}", resultMap);
                        //服务端收到消息发送通过websocket发送到前端
                        UdpWebSocketServer.GroupSentMessage(JSON.toJSONString(resultMap));
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        } catch (Exception ex) {
            ex.printStackTrace();
            System.exit(0);
        }
    }

    public static String[] splitByNumber(String s, int chunkSize){
        int chunkCount = (s.length() / chunkSize) + (s.length() % chunkSize == 0 ? 0 : 1);
        String[] returnVal = new String[chunkCount];
        for(int i=0;i<chunkCount;i++){
            returnVal[i] = s.substring(i*chunkSize, Math.min((i+1)*chunkSize, s.length()));
        }
        return returnVal;
    }
}
