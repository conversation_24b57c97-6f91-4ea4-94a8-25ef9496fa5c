# GIS平台远程控制功能

## 功能概述

GIS平台远程控制功能允许用户通过WebSocket连接实现远程协助和控制，支持一对一的远程控制会话。

## 功能特性

### 核心功能
- **协助模式**：被控制端生成6位数字控制码，等待控制端连接
- **控制模式**：控制端输入控制码连接到被控制端
- **实时通信**：基于WebSocket的低延迟通信
- **命令系统**：可扩展的EventBus命令处理架构
- **安全机制**：控制码10分钟自动过期，支持连接验证

### UI界面
- **协助按钮**：被控制端使用，生成控制码
- **控制按钮**：控制端使用，输入控制码连接
- **状态显示**：实时显示连接状态和角色信息
- **控制操作**：控制端可发送地图操作命令

## 使用方法

### 被控制端操作
1. 点击右侧工具栏上方的"协助"按钮
2. 系统自动生成6位数字控制码
3. 将控制码告知控制端用户
4. 等待控制端连接
5. 连接建立后可看到控制状态

### 控制端操作
1. 点击右侧工具栏上方的"控制"按钮
2. 在弹出对话框中输入6位控制码
3. 点击"连接"按钮
4. 连接成功后可使用控制操作
5. 支持的操作：地图放大、地图缩小

### 断开连接
- 任一方点击"停止控制"按钮即可断开连接
- 控制码过期后自动断开连接
- 网络异常时自动重连

## 技术架构

### 前端架构
```
src/components/RemoteControl/
├── index.vue                           # 主组件
├── RemoteControlWebSocketClient.js     # WebSocket客户端
└── RemoteControlCommandHandler.js      # 命令处理器
```

### 后端架构
```
src/main/java/cn/hczj/gis/remotecontrol/
├── websocket/
│   └── RemoteControlWebSocketServer.java    # WebSocket服务端
├── service/
│   └── RemoteControlService.java            # 业务逻辑服务
├── model/
│   ├── RemoteControlSession.java            # 会话模型
│   └── RemoteControlMessage.java            # 消息模型
├── controller/
│   └── RemoteControlController.java         # REST接口
└── task/
    └── RemoteControlCleanupTask.java        # 清理任务
```

### 配置文件
- `public/configMap.js`：添加了`REMOTE_CONTROL_SERVICES`常量

## 支持的命令

### 地图操作命令
- `MAP_ZOOM_IN`：地图放大
- `MAP_ZOOM_OUT`：地图缩小
- `MAP_PAN`：地图平移
- `MAP_FLY_TO`：飞行到指定位置
- `MAP_RESET`：重置地图视角

### 图层控制命令
- `LAYER_TOGGLE`：切换图层显示状态

## 扩展开发

### 添加新命令
1. 在`RemoteControlCommandHandler.js`中注册新的命令处理器：
```javascript
// 注册新命令
remoteControlCommandHandler.registerHandler('NEW_COMMAND', (data) => {
    // 处理命令逻辑
    console.log('执行新命令:', data);
});
```

2. 在控制端发送命令：
```javascript
this.wsClient.sendCommand('NEW_COMMAND', {
    // 命令数据
    param1: 'value1',
    param2: 'value2'
});
```

### 自定义UI组件
可以通过修改`src/components/RemoteControl/index.vue`来自定义UI界面和交互逻辑。

## 安全考虑

1. **控制码过期**：控制码10分钟自动过期
2. **会话隔离**：每个控制码对应独立的会话
3. **连接验证**：验证控制码格式和有效性
4. **权限控制**：只有控制端可以发送控制命令

## 故障排除

### 常见问题
1. **连接失败**
   - 检查WebSocket服务是否启动
   - 确认控制码是否正确
   - 检查网络连接

2. **命令不响应**
   - 确认EventBus是否正确初始化
   - 检查命令处理器是否注册
   - 查看浏览器控制台错误信息

3. **控制码无效**
   - 确认控制码未过期
   - 检查输入格式是否正确（6位数字）

### 调试信息
- 浏览器控制台会输出详细的连接和命令执行日志
- 后端日志记录会话创建、连接和断开信息

## 性能优化

1. **心跳机制**：30秒心跳保持连接活跃
2. **自动重连**：网络异常时自动重连，最多5次
3. **资源清理**：定时清理过期会话和连接

## 兼容性

- 支持现代浏览器（Chrome、Firefox、Safari、Edge）
- 兼容移动端浏览器
- 不影响现有WebSocket连接（IM、联动等）

## 更新日志

### v1.0.0
- 实现基础远程控制功能
- 支持地图缩放控制
- 完整的连接管理和错误处理
- 可扩展的命令系统架构
