/**
 * 远程控制命令处理器
 * 使用EventBus模式处理各种远程控制命令
 */
class RemoteControlCommandHandler {
    constructor() {
        this.commandHandlers = new Map();
        this.isInitialized = false;
        this.eventBus = null;
    }

    /**
     * 初始化命令处理器
     */
    init() {
        if (this.isInitialized) {
            console.warn('远程控制命令处理器已经初始化');
            return;
        }

        // 获取EventBus
        if (window.Vue && window.Vue.prototype && window.Vue.prototype.$bus) {
            this.eventBus = window.Vue.prototype.$bus;
        } else {
            console.error('EventBus未初始化，无法启动远程控制命令处理器');
            return;
        }

        // 注册默认命令处理器
        this.registerDefaultHandlers();

        // 监听远程控制命令事件
        this.eventBus.$on('remote-control-command', this.handleCommand.bind(this));

        this.isInitialized = true;
        console.log('远程控制命令处理器初始化完成');
    }

    /**
     * 销毁命令处理器
     */
    destroy() {
        if (!this.isInitialized) return;

        if (this.eventBus) {
            this.eventBus.$off('remote-control-command', this.handleCommand.bind(this));
        }

        this.commandHandlers.clear();
        this.isInitialized = false;
        console.log('远程控制命令处理器已销毁');
    }

    /**
     * 注册命令处理器
     * @param {string} commandType 命令类型
     * @param {function} handler 处理函数
     */
    registerHandler(commandType, handler) {
        if (typeof handler !== 'function') {
            console.error('命令处理器必须是函数');
            return;
        }

        this.commandHandlers.set(commandType, handler);
        console.log(`注册远程控制命令处理器: ${commandType}`);
    }

    /**
     * 注销命令处理器
     * @param {string} commandType 命令类型
     */
    unregisterHandler(commandType) {
        if (this.commandHandlers.has(commandType)) {
            this.commandHandlers.delete(commandType);
            console.log(`注销远程控制命令处理器: ${commandType}`);
        }
    }

    /**
     * 处理命令
     * @param {object} command 命令对象
     */
    handleCommand(command) {
        const { type, data } = command;

        if (!type) {
            console.error('远程控制命令缺少类型');
            return;
        }

        const handler = this.commandHandlers.get(type);
        if (!handler) {
            console.warn(`未找到命令处理器: ${type}`);
            return;
        }

        try {
            console.log(`执行远程控制命令: ${type}`, data);
            handler(data);
        } catch (error) {
            console.error(`执行远程控制命令失败: ${type}`, error);
        }
    }

    /**
     * 注册默认命令处理器
     */
    registerDefaultHandlers() {
        // 地图放大命令
        this.registerHandler('MAP_ZOOM_IN', (data) => {
            const factor = data?.factor || 1.5;
            this.zoomMap(factor);
        });

        // 地图缩小命令
        this.registerHandler('MAP_ZOOM_OUT', (data) => {
            const factor = data?.factor || 0.75;
            this.zoomMap(factor);
        });

        // 地图平移命令
        this.registerHandler('MAP_PAN', (data) => {
            if (data && data.x !== undefined && data.y !== undefined) {
                this.panMap(data.x, data.y);
            }
        });

        // 地图定位命令
        this.registerHandler('MAP_FLY_TO', (data) => {
            if (data && data.longitude !== undefined && data.latitude !== undefined) {
                this.flyToLocation(data.longitude, data.latitude, data.height);
            }
        });

        // 地图重置命令
        this.registerHandler('MAP_RESET', () => {
            this.resetMap();
        });

        // 图层控制命令
        this.registerHandler('LAYER_TOGGLE', (data) => {
            if (data && data.layerId) {
                this.toggleLayer(data.layerId, data.visible);
            }
        });
    }

    /**
     * 地图缩放
     * @param {number} factor 缩放因子
     */
    zoomMap(factor) {
        try {
            // 3D地图缩放
            if (window.FreeEarth && window.FreeEarth.camera) {
                const camera = window.FreeEarth.camera;
                const currentHeight = camera.positionCartographic.height;
                const newHeight = currentHeight / factor;
                
                camera.setView({
                    destination: Cesium.Cartesian3.fromRadians(
                        camera.positionCartographic.longitude,
                        camera.positionCartographic.latitude,
                        newHeight
                    )
                });
                
                console.log(`3D地图缩放: ${factor}, 新高度: ${newHeight}`);
            }

            // 2D地图缩放
            if (window.Free2DMap && window.Free2DMap.getView) {
                const view = window.Free2DMap.getView();
                const currentZoom = view.getZoom();
                const zoomChange = factor > 1 ? 1 : -1;
                view.setZoom(currentZoom + zoomChange);
                
                console.log(`2D地图缩放: ${factor}, 新缩放级别: ${currentZoom + zoomChange}`);
            }
        } catch (error) {
            console.error('地图缩放失败:', error);
        }
    }

    /**
     * 地图平移
     * @param {number} x X方向偏移（像素）
     * @param {number} y Y方向偏移（像素）
     */
    panMap(x, y) {
        try {
            // 3D地图平移
            if (window.FreeEarth && window.FreeEarth.camera) {
                const camera = window.FreeEarth.camera;
                const canvas = window.FreeEarth.canvas;
                
                const startPosition = new Cesium.Cartesian2(canvas.clientWidth / 2, canvas.clientHeight / 2);
                const endPosition = new Cesium.Cartesian2(startPosition.x + x, startPosition.y + y);
                
                const startRay = camera.getPickRay(startPosition);
                const endRay = camera.getPickRay(endPosition);
                
                const startCartesian = window.FreeEarth.scene.globe.pick(startRay, window.FreeEarth.scene);
                const endCartesian = window.FreeEarth.scene.globe.pick(endRay, window.FreeEarth.scene);
                
                if (startCartesian && endCartesian) {
                    const offset = Cesium.Cartesian3.subtract(startCartesian, endCartesian, new Cesium.Cartesian3());
                    const newPosition = Cesium.Cartesian3.add(camera.position, offset, new Cesium.Cartesian3());
                    camera.setView({ destination: newPosition });
                }
                
                console.log(`3D地图平移: (${x}, ${y})`);
            }

            // 2D地图平移
            if (window.Free2DMap && window.Free2DMap.getView) {
                const view = window.Free2DMap.getView();
                const center = view.getCenter();
                const resolution = view.getResolution();
                
                const newCenter = [
                    center[0] - x * resolution,
                    center[1] + y * resolution
                ];
                
                view.setCenter(newCenter);
                console.log(`2D地图平移: (${x}, ${y}), 新中心: ${newCenter}`);
            }
        } catch (error) {
            console.error('地图平移失败:', error);
        }
    }

    /**
     * 飞行到指定位置
     * @param {number} longitude 经度
     * @param {number} latitude 纬度
     * @param {number} height 高度（可选）
     */
    flyToLocation(longitude, latitude, height = 10000) {
        try {
            // 3D地图飞行
            if (window.FreeEarth && window.FreeEarth.camera) {
                window.FreeEarth.camera.flyTo({
                    destination: Cesium.Cartesian3.fromDegrees(longitude, latitude, height),
                    duration: 2.0
                });
                
                console.log(`3D地图飞行到: (${longitude}, ${latitude}, ${height})`);
            }

            // 2D地图飞行
            if (window.Free2DMap && window.Free2DMap.getView) {
                const view = window.Free2DMap.getView();
                view.animate({
                    center: ol.proj.fromLonLat([longitude, latitude]),
                    duration: 2000
                });
                
                console.log(`2D地图飞行到: (${longitude}, ${latitude})`);
            }
        } catch (error) {
            console.error('地图飞行失败:', error);
        }
    }

    /**
     * 重置地图
     */
    resetMap() {
        try {
            // 重置到初始视点
            if (window.INIT_VIEWPORT && window.INIT_ROTATION && window.INIT_DURATION) {
                const [longitude, latitude, height] = window.INIT_VIEWPORT;
                
                // 3D地图重置
                if (window.FreeEarth && window.FreeEarth.camera) {
                    window.FreeEarth.camera.flyTo({
                        destination: Cesium.Cartesian3.fromDegrees(longitude, latitude, height),
                        duration: window.INIT_DURATION
                    });
                }

                // 2D地图重置
                if (window.Free2DMap && window.Free2DMap.getView) {
                    const view = window.Free2DMap.getView();
                    view.animate({
                        center: ol.proj.fromLonLat([longitude, latitude]),
                        zoom: 10,
                        duration: window.INIT_DURATION * 1000
                    });
                }
                
                console.log('地图已重置到初始视点');
            }
        } catch (error) {
            console.error('地图重置失败:', error);
        }
    }

    /**
     * 切换图层显示
     * @param {string} layerId 图层ID
     * @param {boolean} visible 是否显示
     */
    toggleLayer(layerId, visible) {
        try {
            // 通过EventBus通知图层管理器
            if (this.eventBus) {
                this.eventBus.$emit('layer-toggle', {
                    layerId: layerId,
                    visible: visible
                });
                
                console.log(`切换图层显示: ${layerId} -> ${visible}`);
            }
        } catch (error) {
            console.error('切换图层失败:', error);
        }
    }
}

// 创建全局实例
const remoteControlCommandHandler = new RemoteControlCommandHandler();

export default remoteControlCommandHandler;
