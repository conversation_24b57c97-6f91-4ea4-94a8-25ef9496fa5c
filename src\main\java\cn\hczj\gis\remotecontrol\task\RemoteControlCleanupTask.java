package cn.hczj.gis.remotecontrol.task;

import cn.hczj.gis.remotecontrol.service.RemoteControlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 远程控制清理任务
 */
@Component
@Slf4j
public class RemoteControlCleanupTask {
    
    @Autowired
    private RemoteControlService remoteControlService;
    
    /**
     * 每5分钟清理一次过期会话
     */
    @Scheduled(fixedRate = 5 * 60 * 1000)
    public void cleanupExpiredSessions() {
        try {
            int beforeCount = remoteControlService.getActiveSessionCount();
            remoteControlService.cleanExpiredSessions();
            int afterCount = remoteControlService.getActiveSessionCount();
            
            if (beforeCount != afterCount) {
                log.info("清理过期远程控制会话，清理前: {}, 清理后: {}", beforeCount, afterCount);
            }
        } catch (Exception e) {
            log.error("清理过期远程控制会话失败", e);
        }
    }
}
