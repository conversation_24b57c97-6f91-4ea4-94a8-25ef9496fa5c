package cn.hczj.gis.remotecontrol.service;

import cn.hczj.gis.remotecontrol.model.RemoteControlSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 远程控制服务
 */
@Service
@Slf4j
public class RemoteControlService {
    
    /**
     * 控制码 -> 会话映射
     */
    private final Map<String, RemoteControlSession> sessions = new ConcurrentHashMap<>();
    
    /**
     * 控制码有效期（分钟）
     */
    private static final int CODE_EXPIRE_MINUTES = 10;
    
    /**
     * 生成6位数字控制码
     */
    public String generateControlCode() {
        String code;
        do {
            code = String.format("%06d", new Random().nextInt(1000000));
        } while (sessions.containsKey(code));
        
        log.info("生成控制码: {}", code);
        return code;
    }
    
    /**
     * 创建远程控制会话
     */
    public RemoteControlSession createSession(String controlCode) {
        RemoteControlSession session = new RemoteControlSession();
        session.setControlCode(controlCode);
        session.setExpireTime(LocalDateTime.now().plusMinutes(CODE_EXPIRE_MINUTES));
        
        sessions.put(controlCode, session);
        log.info("创建远程控制会话，控制码: {}", controlCode);
        
        return session;
    }
    
    /**
     * 根据控制码获取会话
     */
    public RemoteControlSession getSession(String controlCode) {
        RemoteControlSession session = sessions.get(controlCode);
        if (session != null && session.isExpired()) {
            removeSession(controlCode);
            return null;
        }
        return session;
    }
    
    /**
     * 验证控制码
     */
    public boolean validateControlCode(String controlCode) {
        if (controlCode == null || controlCode.length() != 6) {
            return false;
        }
        
        try {
            Integer.parseInt(controlCode);
        } catch (NumberFormatException e) {
            return false;
        }
        
        RemoteControlSession session = getSession(controlCode);
        return session != null && !session.isExpired();
    }
    
    /**
     * 移除会话
     */
    public void removeSession(String controlCode) {
        RemoteControlSession session = sessions.remove(controlCode);
        if (session != null) {
            log.info("移除远程控制会话，控制码: {}", controlCode);
        }
    }
    
    /**
     * 清理过期会话
     */
    public void cleanExpiredSessions() {
        sessions.entrySet().removeIf(entry -> {
            RemoteControlSession session = entry.getValue();
            if (session.isExpired()) {
                log.info("清理过期会话，控制码: {}", entry.getKey());
                return true;
            }
            return false;
        });
    }
    
    /**
     * 获取所有活跃会话数量
     */
    public int getActiveSessionCount() {
        return sessions.size();
    }
}
