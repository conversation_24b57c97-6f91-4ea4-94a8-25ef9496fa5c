package cn.hczj.gis.remotecontrol.controller;

import cn.hczj.gis.remotecontrol.service.RemoteControlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 远程控制REST接口
 */
@RestController
@RequestMapping("/api/remote-control")
@Slf4j
public class RemoteControlController {
    
    @Autowired
    private RemoteControlService remoteControlService;
    
    /**
     * 获取远程控制状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("activeSessionCount", remoteControlService.getActiveSessionCount());
        status.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(status);
    }
    
    /**
     * 验证控制码
     */
    @PostMapping("/validate-code")
    public ResponseEntity<Map<String, Object>> validateCode(@RequestBody Map<String, String> request) {
        String controlCode = request.get("controlCode");
        
        Map<String, Object> response = new HashMap<>();
        boolean isValid = remoteControlService.validateControlCode(controlCode);
        
        response.put("valid", isValid);
        response.put("message", isValid ? "控制码有效" : "控制码无效或已过期");
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 手动清理过期会话
     */
    @PostMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanup() {
        int beforeCount = remoteControlService.getActiveSessionCount();
        remoteControlService.cleanExpiredSessions();
        int afterCount = remoteControlService.getActiveSessionCount();
        
        Map<String, Object> response = new HashMap<>();
        response.put("beforeCount", beforeCount);
        response.put("afterCount", afterCount);
        response.put("cleanedCount", beforeCount - afterCount);
        
        log.info("手动清理过期会话，清理前: {}, 清理后: {}", beforeCount, afterCount);
        
        return ResponseEntity.ok(response);
    }
}
