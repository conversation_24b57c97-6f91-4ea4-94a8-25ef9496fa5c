package cn.hczj.gis.remotecontrol.websocket;

import cn.hczj.gis.remotecontrol.model.RemoteControlMessage;
import cn.hczj.gis.remotecontrol.model.RemoteControlSession;
import cn.hczj.gis.remotecontrol.service.RemoteControlService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 远程控制WebSocket服务
 */
@ServerEndpoint("/remote-control")
@Component
@Slf4j
public class RemoteControlWebSocketServer {
    
    private static RemoteControlService remoteControlService;
    
    /**
     * 会话ID -> WebSocket连接映射
     */
    private static final Map<String, RemoteControlWebSocketServer> connections = new ConcurrentHashMap<>();
    
    /**
     * 当前连接的会话
     */
    private Session session;
    
    /**
     * 连接ID
     */
    private String connectionId;
    
    /**
     * 用户角色：assisted（被控制端）或 controller（控制端）
     */
    private String role;
    
    /**
     * 关联的控制码
     */
    private String controlCode;
    
    @Autowired
    public void setRemoteControlService(RemoteControlService remoteControlService) {
        RemoteControlWebSocketServer.remoteControlService = remoteControlService;
    }
    
    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session) {
        this.session = session;
        this.connectionId = session.getId();
        connections.put(connectionId, this);
        
        log.info("远程控制WebSocket连接建立，连接ID: {}", connectionId);
        
        // 发送连接成功消息
        sendMessage(RemoteControlMessage.success(
            RemoteControlMessage.MessageType.SESSION_STATUS, 
            "连接建立成功"
        ));
    }
    
    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        connections.remove(connectionId);
        
        // 如果有关联的控制码，需要通知对方连接已断开
        if (controlCode != null) {
            RemoteControlSession controlSession = remoteControlService.getSession(controlCode);
            if (controlSession != null) {
                controlSession.setStatus(RemoteControlSession.SessionStatus.DISCONNECTED);
                
                // 通知对方连接已断开
                notifyPeerDisconnection(controlSession);
            }
        }
        
        log.info("远程控制WebSocket连接关闭，连接ID: {}", connectionId);
    }
    
    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("收到远程控制消息，连接ID: {}, 消息: {}", connectionId, message);
        
        try {
            RemoteControlMessage msg = JSON.parseObject(message, RemoteControlMessage.class);
            handleMessage(msg);
        } catch (Exception e) {
            log.error("处理远程控制消息失败", e);
            sendMessage(RemoteControlMessage.error("消息格式错误"));
        }
    }
    
    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("远程控制WebSocket发生错误，连接ID: {}", connectionId, error);
    }
    
    /**
     * 处理消息
     */
    private void handleMessage(RemoteControlMessage msg) {
        switch (msg.getType()) {
            case REQUEST_CONTROL_CODE:
                handleRequestControlCode();
                break;
            case JOIN_SESSION:
                handleJoinSession(msg);
                break;
            case COMMAND:
                handleCommand(msg);
                break;
            case HEARTBEAT:
                handleHeartbeat();
                break;
            case DISCONNECT:
                handleDisconnect();
                break;
            default:
                log.warn("未知消息类型: {}", msg.getType());
        }
    }
    
    /**
     * 处理请求控制码
     */
    private void handleRequestControlCode() {
        String code = remoteControlService.generateControlCode();
        RemoteControlSession controlSession = remoteControlService.createSession(code);
        controlSession.setAssistedSession(session);
        
        this.role = "assisted";
        this.controlCode = code;
        
        RemoteControlMessage response = RemoteControlMessage.success(
            RemoteControlMessage.MessageType.CONTROL_CODE_RESPONSE, 
            "控制码生成成功"
        );
        response.setControlCode(code);
        sendMessage(response);
    }
    
    /**
     * 处理加入会话
     */
    private void handleJoinSession(RemoteControlMessage msg) {
        String code = msg.getControlCode();
        
        if (!remoteControlService.validateControlCode(code)) {
            sendMessage(RemoteControlMessage.error("无效的控制码或控制码已过期"));
            return;
        }
        
        RemoteControlSession controlSession = remoteControlService.getSession(code);
        if (controlSession == null) {
            sendMessage(RemoteControlMessage.error("会话不存在"));
            return;
        }
        
        if (controlSession.getControllerSession() != null) {
            sendMessage(RemoteControlMessage.error("该会话已有控制端连接"));
            return;
        }
        
        // 建立连接
        controlSession.setControllerSession(session);
        controlSession.setStatus(RemoteControlSession.SessionStatus.CONNECTED);
        
        this.role = "controller";
        this.controlCode = code;
        
        // 通知控制端连接成功
        sendMessage(RemoteControlMessage.success(
            RemoteControlMessage.MessageType.SESSION_STATUS, 
            "连接建立成功，您现在可以控制对方的地图"
        ));
        
        // 通知被控制端有控制端连接
        if (controlSession.getAssistedSession() != null && controlSession.getAssistedSession().isOpen()) {
            RemoteControlWebSocketServer assistedConnection = findConnectionBySession(controlSession.getAssistedSession());
            if (assistedConnection != null) {
                assistedConnection.sendMessage(RemoteControlMessage.success(
                    RemoteControlMessage.MessageType.SESSION_STATUS, 
                    "控制端已连接，对方现在可以控制您的地图"
                ));
            }
        }
    }

    /**
     * 处理控制命令
     */
    private void handleCommand(RemoteControlMessage msg) {
        if (!"controller".equals(role)) {
            sendMessage(RemoteControlMessage.error("只有控制端可以发送命令"));
            return;
        }

        RemoteControlSession controlSession = remoteControlService.getSession(controlCode);
        if (controlSession == null || !controlSession.isConnected()) {
            sendMessage(RemoteControlMessage.error("会话不存在或未连接"));
            return;
        }

        // 转发命令到被控制端
        if (controlSession.getAssistedSession() != null && controlSession.getAssistedSession().isOpen()) {
            RemoteControlWebSocketServer assistedConnection = findConnectionBySession(controlSession.getAssistedSession());
            if (assistedConnection != null) {
                assistedConnection.sendMessage(msg);
                log.info("转发控制命令: {} -> {}", msg.getCommandType(), assistedConnection.connectionId);
            }
        }
    }

    /**
     * 处理心跳
     */
    private void handleHeartbeat() {
        RemoteControlMessage response = new RemoteControlMessage(RemoteControlMessage.MessageType.HEARTBEAT);
        response.setMessage("pong");
        sendMessage(response);
    }

    /**
     * 处理断开连接
     */
    private void handleDisconnect() {
        if (controlCode != null) {
            RemoteControlSession controlSession = remoteControlService.getSession(controlCode);
            if (controlSession != null) {
                controlSession.setStatus(RemoteControlSession.SessionStatus.DISCONNECTED);
                notifyPeerDisconnection(controlSession);
                remoteControlService.removeSession(controlCode);
            }
        }
    }

    /**
     * 通知对方连接已断开
     */
    private void notifyPeerDisconnection(RemoteControlSession controlSession) {
        Session peerSession = null;
        if ("assisted".equals(role) && controlSession.getControllerSession() != null) {
            peerSession = controlSession.getControllerSession();
        } else if ("controller".equals(role) && controlSession.getAssistedSession() != null) {
            peerSession = controlSession.getAssistedSession();
        }

        if (peerSession != null && peerSession.isOpen()) {
            RemoteControlWebSocketServer peerConnection = findConnectionBySession(peerSession);
            if (peerConnection != null) {
                peerConnection.sendMessage(RemoteControlMessage.success(
                    RemoteControlMessage.MessageType.SESSION_STATUS,
                    "对方已断开连接"
                ));
            }
        }
    }

    /**
     * 根据Session查找连接
     */
    private RemoteControlWebSocketServer findConnectionBySession(Session targetSession) {
        return connections.values().stream()
            .filter(conn -> conn.session.equals(targetSession))
            .findFirst()
            .orElse(null);
    }

    /**
     * 发送消息
     */
    private void sendMessage(RemoteControlMessage message) {
        try {
            if (session != null && session.isOpen()) {
                session.getBasicRemote().sendText(JSON.toJSONString(message));
            }
        } catch (IOException e) {
            log.error("发送消息失败", e);
        }
    }
}
