/**
 * 远程控制WebSocket客户端
 */
class RemoteControlWebSocketClient {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 3000;
        this.eventHandlers = {};
        this.heartbeatTimer = null;
        this.heartbeatInterval = 30000; // 30秒心跳
    }

    /**
     * 连接WebSocket
     */
    connect() {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            console.warn('WebSocket已经连接');
            return;
        }

        const wsUrl = REMOTE_CONTROL_SERVICES;
        console.log('连接远程控制WebSocket:', wsUrl);

        try {
            this.socket = new WebSocket(wsUrl);
            this.setupEventHandlers();
        } catch (error) {
            console.error('创建WebSocket连接失败:', error);
            this.handleReconnect();
        }
    }

    /**
     * 设置WebSocket事件处理器
     */
    setupEventHandlers() {
        this.socket.onopen = (event) => {
            console.log('远程控制WebSocket连接已建立');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.startHeartbeat();
            this.emit('connected', event);
        };

        this.socket.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                console.log('收到远程控制消息:', message);
                this.handleMessage(message);
            } catch (error) {
                console.error('解析WebSocket消息失败:', error);
            }
        };

        this.socket.onclose = (event) => {
            console.log('远程控制WebSocket连接已关闭:', event.code, event.reason);
            this.isConnected = false;
            this.stopHeartbeat();
            this.emit('disconnected', event);
            
            // 如果不是主动关闭，尝试重连
            if (event.code !== 1000) {
                this.handleReconnect();
            }
        };

        this.socket.onerror = (error) => {
            console.error('远程控制WebSocket错误:', error);
            this.emit('error', error);
        };
    }

    /**
     * 处理消息
     */
    handleMessage(message) {
        const { type } = message;
        
        switch (type) {
            case 'CONTROL_CODE_RESPONSE':
                this.emit('controlCodeGenerated', message);
                break;
            case 'SESSION_STATUS':
                this.emit('sessionStatus', message);
                break;
            case 'COMMAND':
                this.emit('command', message);
                break;
            case 'HEARTBEAT':
                // 心跳响应，不需要特殊处理
                break;
            case 'ERROR':
                this.emit('error', message);
                break;
            default:
                console.warn('未知消息类型:', type);
        }
    }

    /**
     * 发送消息
     */
    sendMessage(message) {
        if (!this.isConnected || !this.socket) {
            console.error('WebSocket未连接，无法发送消息');
            return false;
        }

        try {
            this.socket.send(JSON.stringify(message));
            return true;
        } catch (error) {
            console.error('发送WebSocket消息失败:', error);
            return false;
        }
    }

    /**
     * 请求控制码
     */
    requestControlCode() {
        return this.sendMessage({
            type: 'REQUEST_CONTROL_CODE'
        });
    }

    /**
     * 加入会话
     */
    joinSession(controlCode) {
        return this.sendMessage({
            type: 'JOIN_SESSION',
            controlCode: controlCode
        });
    }

    /**
     * 发送控制命令
     */
    sendCommand(commandType, commandData) {
        return this.sendMessage({
            type: 'COMMAND',
            commandType: commandType,
            commandData: commandData
        });
    }

    /**
     * 断开连接
     */
    disconnect() {
        if (this.socket) {
            this.sendMessage({ type: 'DISCONNECT' });
            this.socket.close(1000, '主动断开连接');
        }
        this.stopHeartbeat();
    }

    /**
     * 开始心跳
     */
    startHeartbeat() {
        this.stopHeartbeat();
        this.heartbeatTimer = setInterval(() => {
            if (this.isConnected) {
                this.sendMessage({ type: 'HEARTBEAT', message: 'ping' });
            }
        }, this.heartbeatInterval);
    }

    /**
     * 停止心跳
     */
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }

    /**
     * 处理重连
     */
    handleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('达到最大重连次数，停止重连');
            this.emit('maxReconnectAttemptsReached');
            return;
        }

        this.reconnectAttempts++;
        const delay = this.reconnectDelay * this.reconnectAttempts;
        
        console.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连...`);
        
        setTimeout(() => {
            this.connect();
        }, delay);
    }

    /**
     * 添加事件监听器
     */
    on(event, handler) {
        if (!this.eventHandlers[event]) {
            this.eventHandlers[event] = [];
        }
        this.eventHandlers[event].push(handler);
    }

    /**
     * 移除事件监听器
     */
    off(event, handler) {
        if (!this.eventHandlers[event]) return;
        
        const index = this.eventHandlers[event].indexOf(handler);
        if (index > -1) {
            this.eventHandlers[event].splice(index, 1);
        }
    }

    /**
     * 触发事件
     */
    emit(event, data) {
        if (!this.eventHandlers[event]) return;
        
        this.eventHandlers[event].forEach(handler => {
            try {
                handler(data);
            } catch (error) {
                console.error('事件处理器执行失败:', error);
            }
        });
    }

    /**
     * 获取连接状态
     */
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            readyState: this.socket ? this.socket.readyState : WebSocket.CLOSED
        };
    }
}

export default RemoteControlWebSocketClient;
